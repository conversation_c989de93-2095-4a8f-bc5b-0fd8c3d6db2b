import { supabase } from './supabase';
import { QueryResult } from './gemini-api';

export interface QueryExecutionResult {
  success: boolean;
  data?: any[];
  error?: string;
  executionTime?: number;
  rowCount?: number;
}

export class QueryExecutor {
  private readonly MAX_RESULTS = 1000;
  private readonly TIMEOUT_MS = 30000; // 30 seconds

  async executeQuery(sql: string, userRole: string, userId?: string): Promise<QueryExecutionResult> {
    const startTime = Date.now();
    
    try {
      // Additional validation before execution
      this.validateQueryForExecution(sql, userRole, userId);
      
      // Execute query with timeout (LIMIT logic is handled in executeWithSupabase)
      const result = await Promise.race([
        this.executeWithSupabase(sql),
        this.createTimeoutPromise()
      ]);
      
      const executionTime = Date.now() - startTime;
      
      return {
        success: true,
        data: result.data || [],
        executionTime,
        rowCount: result.data?.length || 0
      };
    } catch (error) {
      console.error('Query execution failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Query execution failed',
        executionTime: Date.now() - startTime
      };
    }
  }

  private cleanSQL(sql: string): string {
    // Remove all semicolons (not just trailing ones) and normalize whitespace
    let cleaned = sql.trim();
    
    // Remove ALL semicolons from the SQL
    cleaned = cleaned.replace(/;/g, '');
    
    // Normalize whitespace
    cleaned = cleaned.replace(/\s+/g, ' ');
    
    return cleaned;
  }

  private isTopQuery(sql: string): boolean {
    const lowerSQL = sql.toLowerCase();
    const topKeywords = ['top', 'most', 'highest', 'best', 'first', 'max', 'maximum'];
    return topKeywords.some(keyword => lowerSQL.includes(keyword)) || 
           lowerSQL.includes('order by') && lowerSQL.includes('desc');
  }

  private async executeWithSupabase(sql: string): Promise<{ data: any[] }> {
    // First clean the SQL to remove semicolons
    const cleanedSQL = this.cleanSQL(sql);
    
    // Determine appropriate limit
    const isTopQuery = this.isTopQuery(sql);
    const limitValue = isTopQuery ? 5 : this.MAX_RESULTS;
    
    // Add LIMIT if not present  
    const finalSQL = cleanedSQL.toUpperCase().includes('LIMIT') 
      ? cleanedSQL 
      : `${cleanedSQL} LIMIT ${limitValue}`;
    
    console.log('=== SQL EXECUTION ===');
    console.log('Original SQL:', JSON.stringify(sql));
    console.log('Cleaned SQL:', JSON.stringify(cleanedSQL));
    console.log('Final SQL:', JSON.stringify(finalSQL));
    console.log('Is top query:', isTopQuery);
    console.log('Using limit:', limitValue);
    console.log('Has semicolon in original:', sql.includes(';'));
    console.log('Has semicolon in final:', finalSQL.includes(';'));
    
    // Execute SQL using the simple exec function
    const { data, error } = await supabase.rpc('exec', { sql: finalSQL });
    
    if (error) {
      console.error('SQL execution error:', error);
      throw new Error(`Database error: ${error.message}`);
    }
    
    console.log('SQL execution successful, rows returned:', data?.length || 0);
    
    return { data: data || [] };
  }

  private createTimeoutPromise(): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Query execution timed out after ${this.TIMEOUT_MS}ms`));
      }, this.TIMEOUT_MS);
    });
  }

  private validateQueryForExecution(sql: string, userRole: string, userId?: string): void {
    // Ensure it's a SELECT query (allow WITH clauses for CTEs)
    const trimmedSQL = sql.trim().toUpperCase();
    if (!trimmedSQL.startsWith('SELECT') && !trimmedSQL.startsWith('WITH')) {
      throw new Error('Only SELECT queries are allowed');
    }

    // Check for prohibited operations
    const prohibitedPatterns = [
      /DROP\s+/i,
      /DELETE\s+/i,
      /UPDATE\s+/i,
      /INSERT\s+/i,
      /CREATE\s+/i,
      /ALTER\s+/i,
      /TRUNCATE\s+/i,
      /EXEC\s+/i,
      /EXECUTE\s+/i,
      /xp_/i,
      /sp_/i,
      /;\s*DROP/i,
      /;\s*DELETE/i,
      /;\s*UPDATE/i,
      /;\s*INSERT/i,
      /--/,
      /\/\*/,
      /\*\//
    ];

    for (const pattern of prohibitedPatterns) {
      if (pattern.test(sql)) {
        throw new Error('Query contains prohibited operations');
      }
    }

    // Role-based validation
    if (userRole === 'employee' && userId) {
      // Ensure employee queries include user filter
      if (!sql.includes('user_id') && !sql.includes('users.id')) {
        throw new Error('Employee queries must include user restrictions');
      }
    }
  }



  formatResults(data: any[], query: string): QueryResult {
    if (!data || data.length === 0) {
      return {
        type: 'table',
        title: 'No Results',
        data: [],
        summary: 'No data found matching your query.'
      };
    }

    const lowercaseQuery = query.toLowerCase();

    // Check if user explicitly requested a chart/visual
    const explicitChartRequest = this.isExplicitChartRequest(lowercaseQuery);

    // Determine best visualization type
    const resultType = this.determineResultType(data, lowercaseQuery, explicitChartRequest);

    switch (resultType) {
      case 'metric':
        return this.formatMetricResult(data, query);
      case 'chart':
        return this.formatChartResult(data, query);
      case 'both':
        return this.formatBothResult(data, query);
      default:
        return this.formatTableResult(data, query);
    }
  }

  private isExplicitChartRequest(query: string): boolean {
    const chartKeywords = ['chart', 'visual', 'graph', 'plot', 'visualize', 'show me a chart', 'display chart', 'pie chart', 'bar chart'];
    return chartKeywords.some(keyword => query.includes(keyword));
  }

  private determineResultType(data: any[], query: string, explicitChartRequest: boolean): 'table' | 'chart' | 'metric' | 'both' {
    // Single value result
    if (data.length === 1 && Object.keys(data[0]).length === 1) {
      return 'metric';
    }

    // If user explicitly requested a chart, prioritize chart view but also show table
    if (explicitChartRequest && this.isChartableData(data, query)) {
      return 'both';
    }

    // Auto-detect chart-worthy data
    if (this.isChartableData(data, query)) {
      return 'both'; // Show both table and chart for better UX
    }

    return 'table';
  }

  private isTimeSeriesData(data: any[]): boolean {
    const firstRow = data[0];
    const keys = Object.keys(firstRow);
    
    // Look for date/time columns
    return keys.some(key => {
      const value = firstRow[key];
      return key.includes('date') || key.includes('time') || 
             (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}/.test(value));
    });
  }

  private isChartableData(data: any[], query: string): boolean {
    if (!data || data.length === 0) return false;

    const lowercaseQuery = query.toLowerCase();
    const firstRow = data[0];
    const keys = Object.keys(firstRow);
    const values = Object.values(firstRow);

    // Time series data
    if (this.isTimeSeriesData(data)) {
      return true;
    }

    // Aggregated data (GROUP BY, COUNT, SUM, etc.)
    const hasGroupBy = lowercaseQuery.includes('group by');
    const hasAggregation = /count\(|sum\(|avg\(|max\(|min\(/i.test(query);
    if (hasGroupBy || hasAggregation) {
      return true;
    }

    // Percentage data
    const hasPercentageData = keys.some(key =>
      key.toLowerCase().includes('percent') ||
      key.toLowerCase().includes('rate') ||
      key.toLowerCase().includes('ratio') ||
      key.toLowerCase().includes('utilization')
    ) || values.some(value =>
      typeof value === 'number' && value >= 0 && value <= 1
    );

    // Zone/department/category usage data
    const hasUsageData = keys.some(key =>
      key.toLowerCase().includes('zone') ||
      key.toLowerCase().includes('department') ||
      key.toLowerCase().includes('usage') ||
      key.toLowerCase().includes('booking') ||
      key.toLowerCase().includes('desk')
    );

    // Distribution/comparison data (good for pie/bar charts)
    const hasDistributionData = data.length > 1 && data.length <= 20 &&
      keys.length === 2 &&
      values.some(value => typeof value === 'number');

    return hasPercentageData || hasUsageData || hasDistributionData;
  }

  private formatMetricResult(data: any[], query: string): QueryResult {
    const value = Object.values(data[0])[0];
    const label = Object.keys(data[0])[0];
    
    return {
      type: 'metric',
      title: this.generateTitle(query),
      data: [{ value, label }],
      summary: `The result shows ${label}: ${value}`
    };
  }

  private formatChartResult(data: any[], query: string): QueryResult {
    const chartType = this.determineChartType(data, query);

    return {
      type: 'chart',
      title: this.generateTitle(query),
      data,
      chartType,
      summary: `Chart showing ${data.length} data points`
    };
  }

  private formatBothResult(data: any[], query: string): QueryResult {
    const chartType = this.determineChartType(data, query);
    const columns = Object.keys(data[0]);

    return {
      type: 'both',
      title: this.generateTitle(query),
      data,
      chartType,
      columns,
      summary: `Showing ${data.length} results in both table and ${chartType} chart format`
    };
  }

  private formatTableResult(data: any[], query: string): QueryResult {
    const columns = Object.keys(data[0]);
    
    return {
      type: 'table',
      title: this.generateTitle(query),
      data,
      columns,
      summary: `Table showing ${data.length} rows and ${columns.length} columns`
    };
  }

  private determineChartType(data: any[], query: string): 'bar' | 'line' | 'pie' | 'area' {
    const lowercaseQuery = query.toLowerCase();
    const firstRow = data[0];
    const keys = Object.keys(firstRow);

    // Explicit chart type requests
    if (lowercaseQuery.includes('pie chart') || lowercaseQuery.includes('pie')) {
      return 'pie';
    }
    if (lowercaseQuery.includes('bar chart') || lowercaseQuery.includes('bar')) {
      return 'bar';
    }
    if (lowercaseQuery.includes('line chart') || lowercaseQuery.includes('line')) {
      return 'line';
    }

    // Time series data -> line chart
    if (lowercaseQuery.includes('trend') || lowercaseQuery.includes('over time') ||
        this.isTimeSeriesData(data)) {
      return 'line';
    }

    // Percentage/utilization data -> pie chart
    const hasPercentageData = keys.some(key =>
      key.toLowerCase().includes('percent') ||
      key.toLowerCase().includes('rate') ||
      key.toLowerCase().includes('ratio') ||
      key.toLowerCase().includes('utilization')
    );

    // Distribution/breakdown queries -> pie chart
    if (lowercaseQuery.includes('distribution') ||
        lowercaseQuery.includes('breakdown') ||
        lowercaseQuery.includes('by zone') ||
        lowercaseQuery.includes('by department') ||
        hasPercentageData) {
      return 'pie';
    }

    // Zone/department usage -> bar chart
    if (lowercaseQuery.includes('zone') ||
        lowercaseQuery.includes('department') ||
        lowercaseQuery.includes('usage') ||
        lowercaseQuery.includes('popular') ||
        lowercaseQuery.includes('most') ||
        lowercaseQuery.includes('top')) {
      return 'bar';
    }

    // Default to bar chart for most aggregated data
    return 'bar';
  }

  private generateTitle(query: string): string {
    // Extract key terms from query to generate a meaningful title
    const words = query.toLowerCase().split(/\s+/);
    const keyWords = words.filter(word => 
      !['the', 'and', 'or', 'of', 'in', 'on', 'at', 'by', 'for', 'with', 'to', 'from'].includes(word)
    );
    
    if (keyWords.length > 0) {
      return keyWords.slice(0, 5).map(word => 
        word.charAt(0).toUpperCase() + word.slice(1)
      ).join(' ');
    }
    
    return 'Query Results';
  }
}

export const queryExecutor = new QueryExecutor(); 