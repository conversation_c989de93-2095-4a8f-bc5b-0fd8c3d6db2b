import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { CheckCircle2, Clock, Wrench, User, Map } from 'lucide-react';
import { cn } from '@/lib/utils';
import { DeskWithZone } from '@/types';
import { format } from 'date-fns';

interface AdvancedFloorPlanViewProps {
  groupedData: Record<number, Record<string, { zone: any; desks: DeskWithZone[] }>>;
  selectedFloor: string | 'all';
  selectedDate: Date;
  getStatusForDate: (desk: DeskWithZone, date: Date) => string;
  getBookingForDate: (desk: DeskWithZone, date: Date) => any;
  handleDeskClick: (desk: DeskWithZone) => void;
  setSelectedProfile?: (profile: {desk: DeskWithZone, booking: any} | null) => void;
}

export function AdvancedFloorPlanView({
  groupedData,
  selectedFloor,
  selectedDate,
  getStatusForDate,
  getBookingForDate,
  handleDeskClick,
  setSelectedProfile
}: AdvancedFloorPlanViewProps) {

  const getStatusStyling = (status: string) => {
    switch (status) {
      case 'available':
        return {
          background: 'bg-emerald-500 hover:bg-emerald-600',
          ring: 'ring-emerald-300 focus:ring-emerald-400',
          icon: <CheckCircle2 className="w-3 h-3" />
        };
      case 'occupied':
        return {
          background: 'bg-rose-500 hover:bg-rose-600',
          ring: 'ring-rose-300 focus:ring-rose-400',
          icon: <User className="w-3 h-3" />
        };
      case 'maintenance':
        return {
          background: 'bg-amber-500 hover:bg-amber-600',
          ring: 'ring-amber-300 focus:ring-amber-400',
          icon: <Wrench className="w-3 h-3" />
        };
      default:
        return {
          background: 'bg-slate-500 hover:bg-slate-600',
          ring: 'ring-slate-300 focus:ring-slate-400',
          icon: <Clock className="w-3 h-3" />
        };
    }
  };

  const handleDeskClickInternal = (desk: DeskWithZone) => {
    const status = getStatusForDate(desk, selectedDate);
    const booking = getBookingForDate(desk, selectedDate);
    
    if (status === 'occupied' && booking?.users && setSelectedProfile) {
      // Show profile modal for occupied desks
      setSelectedProfile({ desk, booking });
    } else {
      handleDeskClick(desk);
    }
  };

  // Filter data based on selected floor
  const filteredData = selectedFloor === 'all' 
    ? groupedData 
    : { [parseInt(selectedFloor)]: groupedData[parseInt(selectedFloor)] || {} };

  return (
    <div className="space-y-6">

      {/* Floor Plan Views */}
      {Object.entries(filteredData).map(([floorNumber, zones]) => (
        <div key={floorNumber} className="space-y-4">
          <div className="flex items-center gap-2">
            <h2 className="text-2xl font-bold">Floor {floorNumber}</h2>
            <Badge variant="secondary">
              {Object.values(zones).reduce((total, zone) => total + zone.desks.length, 0)} desks
            </Badge>
          </div>

          {Object.entries(zones).map(([zoneId, { zone, desks: zoneDesks }]) => (
            <Card key={zoneId} className="shadow-clay-sm overflow-hidden">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center justify-between">
                  <span>{zone.name}</span>
                  <Badge variant="outline">{zoneDesks.length} desks</Badge>
                </CardTitle>
                {zone.description && (
                  <p className="text-sm text-muted-foreground">{zone.description}</p>
                )}
              </CardHeader>
              
              <CardContent className="p-0">
                {zone.photo ? (
                  <InteractiveFloorPlan
                    zone={zone}
                    desks={zoneDesks}
                    selectedDate={selectedDate}
                    getStatusForDate={getStatusForDate}
                    getBookingForDate={getBookingForDate}
                    handleDeskClick={handleDeskClickInternal}
                    getStatusStyling={getStatusStyling}
                  />
                ) : (
                  <div className="p-6 text-center text-muted-foreground">
                    <Map className="w-12 h-12 mx-auto mb-3 opacity-50" />
                    <p>No floor plan image available for this zone.</p>
                    <p className="text-sm">Upload an image in zone management to enable floor plan view.</p>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      ))}

      {/* No results message */}
      {Object.keys(filteredData).length === 0 && (
        <Card className="shadow-clay-sm">
          <CardContent className="text-center py-8">
            <Map className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground">
              No floor plans found for the selected criteria.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

interface InteractiveFloorPlanProps {
  zone: any;
  desks: DeskWithZone[];
  selectedDate: Date;
  getStatusForDate: (desk: DeskWithZone, date: Date) => string;
  getBookingForDate: (desk: DeskWithZone, date: Date) => any;
  handleDeskClick: (desk: DeskWithZone) => void;
  getStatusStyling: (status: string) => any;
}

function InteractiveFloorPlan({
  zone,
  desks,
  selectedDate,
  getStatusForDate,
  getBookingForDate,
  handleDeskClick,
  getStatusStyling
}: InteractiveFloorPlanProps) {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageDimensions, setImageDimensions] = useState({ width: 0, height: 0 });

  return (
    <div className="relative w-full">
      <div 
        className="relative bg-gray-50 rounded-lg overflow-hidden mx-auto"
        style={{ 
          maxWidth: '100%',
          maxHeight: '70vh',
          aspectRatio: imageDimensions.width && imageDimensions.height 
            ? `${imageDimensions.width} / ${imageDimensions.height}` 
            : 'auto'
        }}
      >
        <img
          src={zone.photo}
          alt={`${zone.name} floor plan`}
          className="w-full h-full object-contain"
          onLoad={(e) => {
            const img = e.target as HTMLImageElement;
            setImageDimensions({
              width: img.naturalWidth,
              height: img.naturalHeight
            });
            setImageLoaded(true);
          }}
          style={{ display: imageLoaded ? 'block' : 'none' }}
        />
        
        {!imageLoaded && (
          <div className="w-full h-96 bg-muted animate-pulse flex items-center justify-center">
            <span className="text-muted-foreground">Loading floor plan...</span>
          </div>
        )}

        {/* Desk Circles Overlay */}
        {imageLoaded && desks.map((desk) => {
          const status = getStatusForDate(desk, selectedDate);
          const booking = getBookingForDate(desk, selectedDate);
          const isOccupied = status === 'occupied' && booking?.users;
          const styling = getStatusStyling(status);

          // Convert coordinates to percentages for responsive positioning
          // Use actual image dimensions to match the admin layout editor
          const xPercent = imageDimensions.width > 0 ? (desk.coordinates.x / imageDimensions.width) * 100 : 0;
          const yPercent = imageDimensions.height > 0 ? (desk.coordinates.y / imageDimensions.height) * 100 : 0;

          return (
            <button
              key={desk.id}
              onClick={() => handleDeskClick(desk)}
              className={cn(
                "absolute transform -translate-x-1/2 -translate-y-1/2",
                "w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12",
                "rounded-full flex items-center justify-center",
                "cursor-pointer transition-all duration-200",
                "hover:scale-110 focus:scale-110 focus:outline-none",
                "ring-2 ring-offset-1 focus:ring-4",
                styling.background,
                styling.ring,
                "shadow-lg hover:shadow-xl"
              )}
              style={{
                left: `${Math.max(5, Math.min(95, xPercent))}%`,
                top: `${Math.max(5, Math.min(95, yPercent))}%`
              }}
              title={isOccupied 
                ? `${desk.name} - Click to view ${booking.users.name}'s profile`
                : `${desk.name} - ${status} on ${format(selectedDate, "MMM dd")}`
              }
            >
              {isOccupied ? (
                <div className="relative">
                  <Avatar className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 ring-1 ring-white">
                    <AvatarImage 
                      src={booking.users.avatar} 
                      alt={booking.users.name}
                      className="object-cover"
                    />
                    <AvatarFallback className="text-xs font-bold bg-white/20 text-white">
                      {booking.users.name.split(' ').map((n: string) => n[0]).join('').toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  {/* Status indicator - green for checked-in, blue for booked */}
                  <div className={`absolute -bottom-0.5 -right-0.5 w-1.5 h-1.5 sm:w-2 sm:h-2 md:w-2.5 md:h-2.5 lg:w-3 lg:h-3 xl:w-4 xl:h-4 rounded-full border border-white sm:border-2 shadow-sm ${
                    booking?.status === 'checked-in' ? 'bg-green-400' : 'bg-blue-400'
                  }`}></div>
                </div>
              ) : (
                <div className="text-white text-xs sm:text-sm font-bold">
                  {desk.name.replace('Desk ', '').replace(/[^0-9A-Z]/gi, '')}
                </div>
              )}


            </button>
          );
        })}
      </div>
    </div>
  );
} 