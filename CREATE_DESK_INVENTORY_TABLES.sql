-- Create item types table for defining what types of equipment exist
CREATE TABLE IF NOT EXISTS item_types (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  is_default BOOLEAN DEFAULT false, -- Whether this item type is included by default on new desks
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create desk items table for tracking individual items assigned to desks
CREATE TABLE IF NOT EXISTS desk_items (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  desk_id UUID NOT NULL REFERENCES desks(id) ON DELETE CASCADE,
  item_type_id UUID NOT NULL REFERENCES item_types(id) ON DELETE CASCADE,
  serial_number VARCHAR(100),
  notes TEXT,
  condition VARCHAR(50) DEFAULT 'good', -- good, fair, poor, maintenance
  installed_date DATE DEFAULT CURRENT_DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure unique serial numbers per item type (but allow null serial numbers)
  CONSTRAINT unique_serial_per_type UNIQUE (item_type_id, serial_number),
  CONSTRAINT valid_condition CHECK (condition IN ('good', 'fair', 'poor', 'maintenance'))
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_desk_items_desk_id ON desk_items(desk_id);
CREATE INDEX IF NOT EXISTS idx_desk_items_item_type_id ON desk_items(item_type_id);
CREATE INDEX IF NOT EXISTS idx_desk_items_serial_number ON desk_items(serial_number) WHERE serial_number IS NOT NULL;

-- Insert some default item types
INSERT INTO item_types (name, description, is_default) VALUES
  ('Monitor', 'Computer monitor/display', true),
  ('Keyboard', 'Computer keyboard', true),
  ('Mouse', 'Computer mouse', true),
  ('Chair', 'Office chair', true),
  ('Headset', 'Audio headset/headphones', false),
  ('Laptop Stand', 'Adjustable laptop stand', false),
  ('Webcam', 'External webcam for video calls', false),
  ('Desk Lamp', 'Task lighting', false),
  ('Phone', 'Desk phone', false),
  ('Docking Station', 'Laptop docking station', false)
ON CONFLICT DO NOTHING;

-- Create trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers for updated_at
CREATE TRIGGER update_item_types_updated_at 
  BEFORE UPDATE ON item_types 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_desk_items_updated_at 
  BEFORE UPDATE ON desk_items 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- Create function to automatically add default items when a new desk is created
CREATE OR REPLACE FUNCTION add_default_items_to_desk()
RETURNS TRIGGER AS $$
BEGIN
    -- Add all default item types to the new desk
    INSERT INTO desk_items (desk_id, item_type_id, condition)
    SELECT NEW.id, id, 'good'
    FROM item_types
    WHERE is_default = true;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to add default items when desk is created
CREATE TRIGGER add_default_items_trigger
  AFTER INSERT ON desks
  FOR EACH ROW
  EXECUTE FUNCTION add_default_items_to_desk();

-- Enable RLS (Row Level Security) if not already enabled
ALTER TABLE item_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE desk_items ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for item_types (readable by all authenticated users, manageable by admins)
CREATE POLICY "item_types_read_authenticated" ON item_types FOR SELECT TO authenticated USING (true);
CREATE POLICY "item_types_write_admin" ON item_types FOR ALL TO authenticated USING (
  EXISTS (
    SELECT 1 FROM users 
    WHERE users.id = auth.uid() 
    AND users.role = 'admin'
  )
);

-- Create RLS policies for desk_items (readable by all authenticated users, manageable by admins)
CREATE POLICY "desk_items_read_authenticated" ON desk_items FOR SELECT TO authenticated USING (true);
CREATE POLICY "desk_items_write_admin" ON desk_items FOR ALL TO authenticated USING (
  EXISTS (
    SELECT 1 FROM users 
    WHERE users.id = auth.uid() 
    AND users.role = 'admin'
  )
); 