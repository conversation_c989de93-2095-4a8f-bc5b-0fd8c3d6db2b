-- Update Desk Coordinates for Floor Plan Layout
-- This script sets up coordinates for the Block A floor plan based on the provided image
-- Coordinates are based on a reference image size of 1000x600 pixels

-- First, let's check current desk setup
SELECT d.id, d.name, d.coordinates, z.name as zone_name, z.floor_number 
FROM desks d 
JOIN zones z ON d.zone_id = z.id 
ORDER BY z.floor_number, z.name, d.name;

-- Example coordinate updates for Block A layout (adjust based on your actual desk names)
-- These coordinates position desks roughly where they appear in your image

-- Desk 1 (top-left area)
UPDATE desks SET coordinates = '{"x": 250, "y": 180}' WHERE name ILIKE '%1%' AND id IN (
  SELECT d.id FROM desks d JOIN zones z ON d.zone_id = z.id WHERE z.name ILIKE '%block a%'
);

-- Desk 2 (left of desk 1)
UPDATE desks SET coordinates = '{"x": 150, "y": 180}' WHERE name ILIKE '%2%' AND id IN (
  SELECT d.id FROM desks d JOIN zones z ON d.zone_id = z.id WHERE z.name ILIKE '%block a%'
);

-- Desk 3 (below desk 1)
UPDATE desks SET coordinates = '{"x": 250, "y": 280}' WHERE name ILIKE '%3%' AND id IN (
  SELECT d.id FROM desks d JOIN zones z ON d.zone_id = z.id WHERE z.name ILIKE '%block a%'
);

-- Desk 4 (below desk 2)  
UPDATE desks SET coordinates = '{"x": 150, "y": 280}' WHERE name ILIKE '%4%' AND id IN (
  SELECT d.id FROM desks d JOIN zones z ON d.zone_id = z.id WHERE z.name ILIKE '%block a%'
);

-- Desk 5 (bottom-left area)
UPDATE desks SET coordinates = '{"x": 150, "y": 400}' WHERE name ILIKE '%5%' AND id IN (
  SELECT d.id FROM desks d JOIN zones z ON d.zone_id = z.id WHERE z.name ILIKE '%block a%'
);

-- Desk 6 (center area, top)
UPDATE desks SET coordinates = '{"x": 400, "y": 320}' WHERE name ILIKE '%6%' AND id IN (
  SELECT d.id FROM desks d JOIN zones z ON d.zone_id = z.id WHERE z.name ILIKE '%block a%'
);

-- Desk 7 (center area, bottom-left)
UPDATE desks SET coordinates = '{"x": 380, "y": 420}' WHERE name ILIKE '%7%' AND id IN (
  SELECT d.id FROM desks d JOIN zones z ON d.zone_id = z.id WHERE z.name ILIKE '%block a%'
);

-- Desk 8 (center area, top-right)
UPDATE desks SET coordinates = '{"x": 480, "y": 320}' WHERE name ILIKE '%8%' AND id IN (
  SELECT d.id FROM desks d JOIN zones z ON d.zone_id = z.id WHERE z.name ILIKE '%block a%'
);

-- Desk 9 (center area, bottom-right)
UPDATE desks SET coordinates = '{"x": 480, "y": 420}' WHERE name ILIKE '%9%' AND id IN (
  SELECT d.id FROM desks d JOIN zones z ON d.zone_id = z.id WHERE z.name ILIKE '%block a%'
);

-- Desk 10 (right area, bottom)
UPDATE desks SET coordinates = '{"x": 650, "y": 420}' WHERE name ILIKE '%10%' AND id IN (
  SELECT d.id FROM desks d JOIN zones z ON d.zone_id = z.id WHERE z.name ILIKE '%block a%'
);

-- Desk 11 (far right)
UPDATE desks SET coordinates = '{"x": 850, "y": 400}' WHERE name ILIKE '%11%' AND id IN (
  SELECT d.id FROM desks d JOIN zones z ON d.zone_id = z.id WHERE z.name ILIKE '%block a%'
);

-- Desk 12 (top-right cluster, bottom-right)
UPDATE desks SET coordinates = '{"x": 750, "y": 280}' WHERE name ILIKE '%12%' AND id IN (
  SELECT d.id FROM desks d JOIN zones z ON d.zone_id = z.id WHERE z.name ILIKE '%block a%'
);

-- Desk 13 (top-right cluster, bottom-left)
UPDATE desks SET coordinates = '{"x": 680, "y": 280}' WHERE name ILIKE '%13%' AND id IN (
  Select d.id FROM desks d JOIN zones z ON d.zone_id = z.id WHERE z.name ILIKE '%block a%'
);

-- Desk 14 (top-right cluster, top-left)
UPDATE desks SET coordinates = '{"x": 680, "y": 200}' WHERE name ILIKE '%14%' AND id IN (
  SELECT d.id FROM desks d JOIN zones z ON d.zone_id = z.id WHERE z.name ILIKE '%block a%'
);

-- Desk 15 (top-right cluster, top-right)
UPDATE desks SET coordinates = '{"x": 750, "y": 200}' WHERE name ILIKE '%15%' AND id IN (
  SELECT d.id FROM desks d JOIN zones z ON d.zone_id = z.id WHERE z.name ILIKE '%block a%'
);

-- Verify the updates
SELECT d.id, d.name, d.coordinates, z.name as zone_name, z.floor_number 
FROM desks d 
JOIN zones z ON d.zone_id = z.id 
WHERE d.coordinates != '{"x": 0, "y": 0}'
ORDER BY z.floor_number, z.name, d.name;

-- Notes:
-- 1. Adjust the zone name matching condition (z.name ILIKE '%block a%') to match your actual zone names
-- 2. Adjust the desk name matching conditions (name ILIKE '%1%') to match your actual desk naming
-- 3. These coordinates assume a 1000x600 pixel reference image
-- 4. You can fine-tune positions using the drag-and-drop editor in the admin interface
-- 5. Use the FloorPlanLayoutEditor component to visually adjust positions after running this script 