import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { DeskWithZone, ItemType, DeskItemWithType, ItemCondition } from '@/types';
import { Plus, Edit, Trash2, Package, AlertCircle, CheckCircle, Clock, Wrench } from 'lucide-react';

interface DeskInventoryManagerProps {
  desk: DeskWithZone;
}

export function DeskInventoryManager({ desk }: DeskInventoryManagerProps) {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<DeskItemWithType | null>(null);
  const [newItem, setNewItem] = useState({
    item_type_id: '',
    serial_number: '',
    notes: '',
    condition: 'good' as ItemCondition,
    installed_date: new Date().toISOString().split('T')[0]
  });
  const [editItem, setEditItem] = useState({
    serial_number: '',
    notes: '',
    condition: 'good' as ItemCondition,
    installed_date: ''
  });

  const queryClient = useQueryClient();

  // Fetch desk items with item types
  const { data: deskItems, isLoading: itemsLoading } = useQuery({
    queryKey: ['desk-items', desk.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('desk_items')
        .select(`
          *,
          item_type:item_types(*)
        `)
        .eq('desk_id', desk.id)
        .order('created_at', { ascending: true });

      if (error) throw error;
      return data as DeskItemWithType[];
    },
  });

  // Fetch all item types for dropdown
  const { data: itemTypes } = useQuery({
    queryKey: ['item-types'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('item_types')
        .select('*')
        .order('name', { ascending: true });
      
      if (error) throw error;
      return data as ItemType[];
    },
  });

  // Mutation to add new item
  const addItem = useMutation({
    mutationFn: async (item: typeof newItem) => {
      const { error } = await supabase
        .from('desk_items')
        .insert({
          desk_id: desk.id,
          item_type_id: item.item_type_id,
          serial_number: item.serial_number || null,
          notes: item.notes || null,
          condition: item.condition,
          installed_date: item.installed_date
        });
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['desk-items', desk.id] });
      toast.success('Item added successfully');
      setIsAddDialogOpen(false);
      setNewItem({
        item_type_id: '',
        serial_number: '',
        notes: '',
        condition: 'good',
        installed_date: new Date().toISOString().split('T')[0]
      });
    },
    onError: (error: any) => {
      toast.error('Failed to add item: ' + error.message);
    },
  });

  // Mutation to update item
  const updateItem = useMutation({
    mutationFn: async ({ itemId, updates }: { itemId: string; updates: typeof editItem }) => {
      const { error } = await supabase
        .from('desk_items')
        .update({
          serial_number: updates.serial_number || null,
          notes: updates.notes || null,
          condition: updates.condition,
          installed_date: updates.installed_date
        })
        .eq('id', itemId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['desk-items', desk.id] });
      toast.success('Item updated successfully');
      setIsEditDialogOpen(false);
      setSelectedItem(null);
    },
    onError: (error: any) => {
      toast.error('Failed to update item: ' + error.message);
    },
  });

  // Mutation to delete item
  const deleteItem = useMutation({
    mutationFn: async (itemId: string) => {
      const { error } = await supabase
        .from('desk_items')
        .delete()
        .eq('id', itemId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['desk-items', desk.id] });
      toast.success('Item removed successfully');
    },
    onError: (error: any) => {
      toast.error('Failed to remove item: ' + error.message);
    },
  });

  const handleAddItem = () => {
    if (!newItem.item_type_id) {
      toast.error('Please select an item type');
      return;
    }
    addItem.mutate(newItem);
  };

  const handleEditItem = (item: DeskItemWithType) => {
    setSelectedItem(item);
    setEditItem({
      serial_number: item.serial_number || '',
      notes: item.notes || '',
      condition: item.condition,
      installed_date: item.installed_date
    });
    setIsEditDialogOpen(true);
  };

  const handleUpdateItem = () => {
    if (!selectedItem) return;
    updateItem.mutate({
      itemId: selectedItem.id,
      updates: editItem
    });
  };

  const handleDeleteItem = (item: DeskItemWithType) => {
    if (confirm(`Are you sure you want to remove ${item.item_type.name} from this desk?`)) {
      deleteItem.mutate(item.id);
    }
  };

  const getConditionIcon = (condition: ItemCondition) => {
    switch (condition) {
      case 'good':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'fair':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'poor':
        return <AlertCircle className="h-4 w-4 text-orange-500" />;
      case 'maintenance':
        return <Wrench className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getConditionBadge = (condition: ItemCondition) => {
    switch (condition) {
      case 'good':
        return <Badge className="bg-green-500">Good</Badge>;
      case 'fair':
        return <Badge className="bg-yellow-500">Fair</Badge>;
      case 'poor':
        return <Badge className="bg-orange-500">Poor</Badge>;
      case 'maintenance':
        return <Badge className="bg-red-500">Maintenance</Badge>;
      default:
        return <Badge variant="secondary">{condition}</Badge>;
    }
  };

  // Get available item types (not already assigned to this desk)
  const availableItemTypes = itemTypes?.filter(itemType => 
    !deskItems?.some(deskItem => deskItem.item_type_id === itemType.id)
  ) || [];

  if (itemsLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Desk Inventory
            </CardTitle>
            <CardDescription>
              Manage items and equipment for {desk.name}
            </CardDescription>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Item
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add Item to {desk.name}</DialogTitle>
                <DialogDescription>
                  Add a new item or equipment to this desk
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="item-type" className="text-right">
                    Item Type
                  </Label>
                  <Select value={newItem.item_type_id} onValueChange={(value) => setNewItem({ ...newItem, item_type_id: value })}>
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select item type" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableItemTypes.map((itemType) => (
                        <SelectItem key={itemType.id} value={itemType.id}>
                          {itemType.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="serial-number" className="text-right">
                    Serial Number
                  </Label>
                  <Input
                    id="serial-number"
                    value={newItem.serial_number}
                    onChange={(e) => setNewItem({ ...newItem, serial_number: e.target.value })}
                    className="col-span-3"
                    placeholder="Optional"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="condition" className="text-right">
                    Condition
                  </Label>
                  <Select value={newItem.condition} onValueChange={(value: ItemCondition) => setNewItem({ ...newItem, condition: value })}>
                    <SelectTrigger className="col-span-3">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="good">Good</SelectItem>
                      <SelectItem value="fair">Fair</SelectItem>
                      <SelectItem value="poor">Poor</SelectItem>
                      <SelectItem value="maintenance">Maintenance</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="installed-date" className="text-right">
                    Installed Date
                  </Label>
                  <Input
                    id="installed-date"
                    type="date"
                    value={newItem.installed_date}
                    onChange={(e) => setNewItem({ ...newItem, installed_date: e.target.value })}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="notes" className="text-right">
                    Notes
                  </Label>
                  <Textarea
                    id="notes"
                    value={newItem.notes}
                    onChange={(e) => setNewItem({ ...newItem, notes: e.target.value })}
                    className="col-span-3"
                    placeholder="Additional notes (optional)"
                    rows={3}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button onClick={handleAddItem} disabled={addItem.isPending}>
                  {addItem.isPending ? 'Adding...' : 'Add Item'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {!deskItems || deskItems.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No items assigned to this desk yet.</p>
            <p className="text-sm">Click "Add Item" to start managing inventory.</p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Item Type</TableHead>
                <TableHead>Serial Number</TableHead>
                <TableHead>Condition</TableHead>
                <TableHead>Installed Date</TableHead>
                <TableHead>Notes</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {deskItems.map((item) => (
                <TableRow key={item.id}>
                  <TableCell className="font-medium">
                    {item.item_type.name}
                  </TableCell>
                  <TableCell>
                    {item.serial_number ? (
                      <code className="text-xs bg-muted px-2 py-1 rounded">
                        {item.serial_number}
                      </code>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getConditionIcon(item.condition)}
                      {getConditionBadge(item.condition)}
                    </div>
                  </TableCell>
                  <TableCell>
                    {new Date(item.installed_date).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    {item.notes ? (
                      <span className="text-sm">{item.notes}</span>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditItem(item)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteItem(item)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}

        {/* Edit Item Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit {selectedItem?.item_type.name}</DialogTitle>
              <DialogDescription>
                Update item details for {desk.name}
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-serial-number" className="text-right">
                  Serial Number
                </Label>
                <Input
                  id="edit-serial-number"
                  value={editItem.serial_number}
                  onChange={(e) => setEditItem({ ...editItem, serial_number: e.target.value })}
                  className="col-span-3"
                  placeholder="Optional"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-condition" className="text-right">
                  Condition
                </Label>
                <Select value={editItem.condition} onValueChange={(value: ItemCondition) => setEditItem({ ...editItem, condition: value })}>
                  <SelectTrigger className="col-span-3">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="good">Good</SelectItem>
                    <SelectItem value="fair">Fair</SelectItem>
                    <SelectItem value="poor">Poor</SelectItem>
                    <SelectItem value="maintenance">Maintenance</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-installed-date" className="text-right">
                  Installed Date
                </Label>
                <Input
                  id="edit-installed-date"
                  type="date"
                  value={editItem.installed_date}
                  onChange={(e) => setEditItem({ ...editItem, installed_date: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-notes" className="text-right">
                  Notes
                </Label>
                <Textarea
                  id="edit-notes"
                  value={editItem.notes}
                  onChange={(e) => setEditItem({ ...editItem, notes: e.target.value })}
                  className="col-span-3"
                  placeholder="Additional notes (optional)"
                  rows={3}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleUpdateItem} disabled={updateItem.isPending}>
                {updateItem.isPending ? 'Updating...' : 'Update Item'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
} 