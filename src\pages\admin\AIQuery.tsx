import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Bot, Search, Clock, TrendingUp, Users, BarChart3, Lightbulb, History, AlertCircle, Loader2, ChevronDown, ChevronUp } from 'lucide-react';
import { QueryResultDisplay } from '@/components/reports/QueryResultDisplay';
import { toast } from 'sonner';
import { aiQueryService } from '@/lib/ai-query-service';
import { useAuth } from '@/contexts/AuthContext';

interface QueryResult {
  type: 'table' | 'chart' | 'metric' | 'both';
  title: string;
  data: any[];
  chartType?: 'bar' | 'line' | 'pie' | 'area';
  columns?: string[];
  summary?: string;
  sql?: string;
}

interface RecentQuery {
  id: string;
  natural_language_query: string;
  success: boolean;
  result_count: number;
  execution_time_ms: number;
  created_at: string;
}

export default function AIQuery() {
  const [query, setQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<QueryResult | null>(null);
  const [recentQueries, setRecentQueries] = useState<RecentQuery[]>([]);
  const [showAllRecentQueries, setShowAllRecentQueries] = useState(false);
  const [rateLimit, setRateLimit] = useState<{
    remaining: number;
    resetTime: string;
  } | null>(null);
  const { user, appUser } = useAuth();

  const exampleQueries = [
    {
      category: 'Popular Queries',
      queries: [
        'How many desks are booked today?',
        'Show me the most popular zones this week',
        'Which departments book the most desks?',
        'What are the peak booking hours?'
      ]
    },
    {
      category: 'User Information',
      queries: [
        'Show me all users from Engineering department',
        'List all managers and their departments',
        'Who are the most active desk bookers?',
        'Show users who joined this month'
      ]
    },
    {
      category: 'Department Insights',
      queries: [
        'Compare booking patterns between Engineering and Sales',
        'Show desk utilization by department over the last month',
        'Which departments prefer morning vs afternoon slots?',
        'Department-wise average booking duration'
      ]
    },
    {
      category: 'Time-Based Analysis',
      queries: [
        'Show booking trends over the last 30 days',
        'Compare this week vs last week desk usage',
        'Peak hours for desk bookings by day of week',
        'Monthly booking growth rate'
      ]
    },
    {
      category: 'Space Utilization',
      queries: [
        'Which desks are never booked?',
        'Show utilization rate by zone',
        'Most and least popular desks',
        'Optimal desk allocation recommendations'
      ]
    }
  ];

  useEffect(() => {
    loadRecentQueries();
    loadRateLimit();
  }, []);

  const loadRecentQueries = async () => {
    try {
      const response = await aiQueryService.getRecentQueries();
      if (response.success && response.queries) {
        setRecentQueries(response.queries);
      }
    } catch (error) {
      console.error('Failed to load recent queries:', error);
    }
  };

  const loadRateLimit = async () => {
    try {
      const response = await aiQueryService.getUserRateLimit();
      if (response.success) {
        setRateLimit({
          remaining: response.remaining || 0,
          resetTime: response.resetTime || ''
        });
      }
    } catch (error) {
      console.error('Failed to load rate limit:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!query.trim()) {
      toast.error('Please enter a query');
      return;
    }

    if (rateLimit && rateLimit.remaining <= 0) {
      toast.error('Rate limit exceeded. Please try again later.');
      return;
    }

    setIsLoading(true);
    setResult(null);

    try {
      const response = await aiQueryService.processQuery({
        query: query.trim(),
        userId: user?.id,
        userRole: appUser?.role || 'employee'
      });

      if (response.success && response.result) {
        setResult(response.result);
        toast.success(`Query completed in ${response.executionTime}ms`);
        
        // Refresh rate limit and recent queries
        loadRateLimit();
        loadRecentQueries();
      } else {
        if (response.rateLimitExceeded) {
          toast.error('Rate limit exceeded. Please try again later.');
        } else {
          toast.error(response.error || 'Query failed');
        }
      }
    } catch (error) {
      console.error('Query error:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleExampleQuery = (exampleQuery: string) => {
    setQuery(exampleQuery);
  };

  const handleRecentQuery = (recentQuery: RecentQuery) => {
    setQuery(recentQuery.natural_language_query);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">AI Query Assistant</h1>
          <p className="text-muted-foreground mt-2">
            Ask questions about your workspace data in natural language
          </p>
        </div>
        <Badge variant="secondary" className="bg-gradient-to-r from-blue-500 to-purple-600 text-white">
          <Bot className="h-4 w-4 mr-1" />
          AI Powered
        </Badge>
      </div>

      {/* Rate Limit Display */}
      {rateLimit && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">
                  Rate Limit: {rateLimit.remaining} queries remaining
                </span>
              </div>
              <span className="text-xs text-muted-foreground">
                Resets: {new Date(rateLimit.resetTime).toLocaleString()}
              </span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Query Input */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Search className="h-5 w-5" />
            <span>Ask Your Question</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="flex space-x-2">
              <Input
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="Ask anything about your workspace data..."
                className="flex-1"
                disabled={isLoading}
              />
              <Button type="submit" disabled={isLoading || !query.trim()}>
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <Search className="h-4 w-4 mr-2" />
                    Ask
                  </>
                )}
              </Button>
            </div>
            <p className="text-sm text-muted-foreground">
              Press Enter to submit your query. Results will appear below.
            </p>
          </form>
        </CardContent>
      </Card>

      {/* Results */}
      {result && (
        <Card>
          <CardHeader>
            <CardTitle>Query Results</CardTitle>
          </CardHeader>
          <CardContent>
            <QueryResultDisplay result={result} />
          </CardContent>
        </Card>
      )}

      {/* Recent Queries */}
      {recentQueries.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center space-x-2">
                <History className="h-5 w-5" />
                <span>Recent Queries</span>
                <Badge variant="outline" className="ml-2">
                  {recentQueries.length}
                </Badge>
              </CardTitle>
              {recentQueries.length > 3 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowAllRecentQueries(!showAllRecentQueries)}
                  className="flex items-center space-x-1"
                >
                  <span className="text-sm">
                    {showAllRecentQueries ? 'Show less' : `Show all ${recentQueries.length}`}
                  </span>
                  {showAllRecentQueries ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {(showAllRecentQueries ? recentQueries : recentQueries.slice(0, 3)).map((recentQuery) => (
                <div
                  key={recentQuery.id}
                  className="flex items-center justify-between p-3 rounded-lg border hover:bg-muted/50 cursor-pointer transition-colors"
                  onClick={() => handleRecentQuery(recentQuery)}
                >
                  <div className="flex-1">
                    <p className="text-sm font-medium">{recentQuery.natural_language_query}</p>
                    <p className="text-xs text-muted-foreground">
                      {recentQuery.success ? (
                        `${recentQuery.result_count} results • ${recentQuery.execution_time_ms}ms`
                      ) : (
                        'Query failed'
                      )}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    {recentQuery.success ? (
                      <Badge variant="secondary" className="bg-green-100 text-green-800">
                        Success
                      </Badge>
                    ) : (
                      <Badge variant="secondary" className="bg-red-100 text-red-800">
                        Failed
                      </Badge>
                    )}
                    <span className="text-xs text-muted-foreground">
                      {new Date(recentQuery.created_at).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
            {!showAllRecentQueries && recentQueries.length > 3 && (
              <div className="mt-3 pt-3 border-t">
                <p className="text-xs text-muted-foreground text-center">
                  {recentQueries.length - 3} more queries hidden
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Example Queries */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {exampleQueries.map((category) => (
          <Card key={category.category}>
            <CardHeader>
              <CardTitle className="text-lg">{category.category}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {category.queries.map((exampleQuery) => (
                  <button
                    key={exampleQuery}
                    onClick={() => handleExampleQuery(exampleQuery)}
                    className="w-full text-left p-3 rounded-lg border hover:bg-muted/50 transition-colors"
                  >
                    <p className="text-sm">{exampleQuery}</p>
                  </button>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Lightbulb className="h-5 w-5" />
            <span>Tips for Better Results</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div>
              <h4 className="font-medium mb-2">Be Specific</h4>
              <p className="text-sm text-muted-foreground">
                Include time periods, departments, or specific metrics for more accurate results.
              </p>
            </div>
            <div>
              <h4 className="font-medium mb-2">Use Natural Language</h4>
              <p className="text-sm text-muted-foreground">
                Ask questions as you would to a colleague - the AI understands context.
              </p>
            </div>
            <div>
              <h4 className="font-medium mb-2">Available Data</h4>
              <p className="text-sm text-muted-foreground">
                You can query data about: users, departments, desks, zones, bookings, and time periods. User queries will be displayed as interactive cards with avatars and role information.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 