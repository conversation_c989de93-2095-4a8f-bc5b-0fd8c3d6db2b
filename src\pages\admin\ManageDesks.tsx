import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { DeskWithZone, Zone } from '@/types';
import { Building2, Plus, Search, Wrench, CheckCircle, XCircle, Edit, Trash2, Filter, QrCode, Package } from 'lucide-react';
import { DeskQRCodeGenerator } from '@/components/admin/DeskQRCodeGenerator';
import { DeskInventoryManager } from '@/components/admin/DeskInventoryManager';
import { ItemTypesManager } from '@/components/admin/ItemTypesManager';

export function ManageDesks() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFloor, setSelectedFloor] = useState<number | 'all'>('all');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isQRCodeDialogOpen, setIsQRCodeDialogOpen] = useState(false);
  const [isInventoryDialogOpen, setIsInventoryDialogOpen] = useState(false);
  const [selectedDesk, setSelectedDesk] = useState<DeskWithZone | null>(null);
  const [newDesk, setNewDesk] = useState({
    name: '',
    zone_id: '',
    coordinates: { x: 0, y: 0 }
  });
  const [editDesk, setEditDesk] = useState({
    name: '',
    zone_id: ''
  });
  const queryClient = useQueryClient();

  // Fetch desks with zones
  const { data: rawDesks, isLoading: desksLoading } = useQuery({
    queryKey: ['admin-desks'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('desks')
        .select(`
          *,
          zone:zones(*)
        `);

      if (error) throw error;
      return data as DeskWithZone[];
    },
  });

  // Group desks by floor and zone, similar to FloorPlan.tsx
  const groupedData = rawDesks?.reduce((acc: Record<number, Record<string, { zone: Zone; desks: DeskWithZone[] }>>, desk) => {
    const floorNumber = desk.zone.floor_number;
    const zoneId = desk.zone.id;

    if (!acc[floorNumber]) {
      acc[floorNumber] = {};
    }

    if (!acc[floorNumber][zoneId]) {
      acc[floorNumber][zoneId] = {
        zone: desk.zone,
        desks: []
      };
    }

    acc[floorNumber][zoneId].desks.push(desk);
    return acc;
  }, {}) || {};

  // Sort desks within each zone naturally (a1, a2, a3, a10 instead of a1, a10, a2, a3)
  if (groupedData) {
    Object.values(groupedData).forEach(floor => {
      Object.values(floor).forEach(zone => {
        zone.desks.sort((a, b) => {
          // Extract the number from desk names (e.g., "A10" -> 10, "Desk A10" -> 10)
          const getNumber = (name: string) => {
            const match = name.match(/(\d+)/);
            return match ? parseInt(match[1], 10) : 0;
          };

          const numA = getNumber(a.name);
          const numB = getNumber(b.name);

          // If numbers are the same, fall back to alphabetical sorting
          if (numA === numB) {
            return a.name.localeCompare(b.name);
          }

          return numA - numB;
        });
      });
    });
  }

  // Flatten grouped data for filtering while maintaining the grouping structure
  const allDesks = Object.values(groupedData).flatMap(floor =>
    Object.values(floor).flatMap(zone => zone.desks)
  );

  // Fetch zones for dropdown
  const { data: zones } = useQuery({
    queryKey: ['zones'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('zones')
        .select('*')
        .order('floor_number', { ascending: true });
      
      if (error) throw error;
      return data as Zone[];
    },
  });

  // Get available floors from grouped data
  const availableFloors = Object.keys(groupedData).map(Number).sort();

  // Filter zones based on selected floor for the dropdown
  const filteredZones = zones?.filter(zone =>
    selectedFloor === 'all' || zone.floor_number === selectedFloor
  ) || [];

  // Filter grouped data based on search term and selected floor
  const filteredGroupedData = Object.entries(groupedData).reduce((acc, [floorNum, floorData]) => {
    const floorNumber = parseInt(floorNum);

    // Skip floor if not selected
    if (selectedFloor !== 'all' && selectedFloor !== floorNumber) {
      return acc;
    }

    const filteredFloorData = Object.entries(floorData).reduce((floorAcc, [zoneId, zoneData]) => {
      const filteredDesks = zoneData.desks.filter(desk => {
        const matchesSearch = desk.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          desk.zone.name.toLowerCase().includes(searchTerm.toLowerCase());
        return matchesSearch;
      });

      // Only include zone if it has matching desks
      if (filteredDesks.length > 0) {
        floorAcc[zoneId] = {
          ...zoneData,
          desks: filteredDesks
        };
      }

      return floorAcc;
    }, {} as Record<string, { zone: Zone; desks: DeskWithZone[] }>);

    // Only include floor if it has zones with matching desks
    if (Object.keys(filteredFloorData).length > 0) {
      acc[floorNumber] = filteredFloorData;
    }

    return acc;
  }, {} as Record<number, Record<string, { zone: Zone; desks: DeskWithZone[] }>>);

  // Get total count of filtered desks for stats
  const filteredDesks = Object.values(filteredGroupedData).flatMap(floor =>
    Object.values(floor).flatMap(zone => zone.desks)
  );

  // Mutation to update desk status
  const updateDeskStatus = useMutation({
    mutationFn: async ({ deskId, status }: { deskId: string; status: 'available' | 'occupied' | 'maintenance' }) => {
      const { error } = await supabase
        .from('desks')
        .update({ status })
        .eq('id', deskId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-desks'] });
      toast.success('Desk status updated successfully');
    },
    onError: (error) => {
      toast.error('Failed to update desk status: ' + error.message);
    },
  });

  // Mutation to create new desk
  const createDesk = useMutation({
    mutationFn: async (desk: typeof newDesk) => {
      const { error } = await supabase
        .from('desks')
        .insert({
          name: desk.name,
          zone_id: desk.zone_id,
          coordinates: desk.coordinates,
          status: 'available'
        });
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-desks'] });
      toast.success('Desk created successfully');
      setIsDialogOpen(false);
      setNewDesk({ name: '', zone_id: '', coordinates: { x: 0, y: 0 } });
    },
    onError: (error) => {
      toast.error('Failed to create desk: ' + error.message);
    },
  });

  // Mutation to update desk details
  const updateDesk = useMutation({
    mutationFn: async ({ deskId, updates }: { deskId: string; updates: { name: string; zone_id: string } }) => {
      const { error } = await supabase
        .from('desks')
        .update({
          name: updates.name,
          zone_id: updates.zone_id
        })
        .eq('id', deskId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-desks'] });
      toast.success('Desk updated successfully');
      setIsEditDialogOpen(false);
      setSelectedDesk(null);
    },
    onError: (error) => {
      toast.error('Failed to update desk: ' + error.message);
    },
  });

  // Mutation to delete desk
  const deleteDesk = useMutation({
    mutationFn: async (deskId: string) => {
      const { error } = await supabase
        .from('desks')
        .delete()
        .eq('id', deskId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-desks'] });
      toast.success('Desk deleted successfully');
      setIsDeleteDialogOpen(false);
      setSelectedDesk(null);
    },
    onError: (error) => {
      toast.error('Failed to delete desk: ' + error.message);
    },
  });

  const handleStatusChange = (deskId: string, status: 'available' | 'occupied' | 'maintenance') => {
    updateDeskStatus.mutate({ deskId, status });
  };

  const handleCreateDesk = () => {
    if (!newDesk.name || !newDesk.zone_id) {
      toast.error('Please fill in all required fields');
      return;
    }
    createDesk.mutate(newDesk);
  };

  const handleEditDesk = (desk: DeskWithZone) => {
    setSelectedDesk(desk);
    setEditDesk({
      name: desk.name,
      zone_id: desk.zone_id
    });
    setIsEditDialogOpen(true);
  };

  const handleUpdateDesk = () => {
    if (!editDesk.name || !editDesk.zone_id || !selectedDesk) {
      toast.error('Please fill in all required fields');
      return;
    }
    updateDesk.mutate({
      deskId: selectedDesk.id,
      updates: editDesk
    });
  };

  const handleDeleteClick = (desk: DeskWithZone) => {
    setSelectedDesk(desk);
    setIsDeleteDialogOpen(true);
  };

  const handleConfirmDelete = () => {
    if (!selectedDesk) return;
    deleteDesk.mutate(selectedDesk.id);
  };

  const handleQRCodeClick = (desk: DeskWithZone) => {
    setSelectedDesk(desk);
    setIsQRCodeDialogOpen(true);
  };

  const handleInventoryClick = (desk: DeskWithZone) => {
    setSelectedDesk(desk);
    setIsInventoryDialogOpen(true);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'available':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'occupied':
        return <XCircle className="h-4 w-4 text-orange-500" />;
      case 'maintenance':
        return <Wrench className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'available':
        return <Badge className="bg-green-500">Available</Badge>;
      case 'occupied':
        return <Badge className="bg-orange-500">Occupied</Badge>;
      case 'maintenance':
        return <Badge className="bg-red-500">Maintenance</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (desksLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Manage Desks</h1>
          <p className="text-muted-foreground">
            Configure desk availability and workspace layout
          </p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Desk
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Desk</DialogTitle>
              <DialogDescription>
                Create a new desk in your workspace
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Name
                </Label>
                <Input
                  id="name"
                  value={newDesk.name}
                  onChange={(e) => setNewDesk({ ...newDesk, name: e.target.value })}
                  className="col-span-3"
                  placeholder="e.g., Desk A1"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="zone" className="text-right">
                  Zone
                </Label>
                <Select value={newDesk.zone_id} onValueChange={(value) => setNewDesk({ ...newDesk, zone_id: value })}>
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select a zone" />
                  </SelectTrigger>
                  <SelectContent>
                    {filteredZones.map((zone) => (
                      <SelectItem key={zone.id} value={zone.id}>
                        {zone.name} (Floor {zone.floor_number})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button onClick={handleCreateDesk} disabled={createDesk.isPending}>
                {createDesk.isPending ? 'Creating...' : 'Create Desk'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Floor Filter Tabs */}
      <Card className="shadow-clay-sm">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filter by Floor
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button
              variant={selectedFloor === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedFloor('all')}
            >
              All Floors ({allDesks?.length || 0} desks)
            </Button>
            {availableFloors.map((floor) => {
              const floorDeskCount = allDesks?.filter(desk => desk.zone.floor_number === floor).length || 0;
              return (
                <Button
                  key={floor}
                  variant={selectedFloor === floor ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedFloor(floor)}
                >
                  Floor {floor} ({floorDeskCount} desks)
                </Button>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Edit Desk Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Desk</DialogTitle>
            <DialogDescription>
              Update desk information
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-name" className="text-right">
                Name
              </Label>
              <Input
                id="edit-name"
                value={editDesk.name}
                onChange={(e) => setEditDesk({ ...editDesk, name: e.target.value })}
                className="col-span-3"
                placeholder="e.g., Desk A1"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-zone" className="text-right">
                Zone
              </Label>
              <Select value={editDesk.zone_id} onValueChange={(value) => setEditDesk({ ...editDesk, zone_id: value })}>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select a zone" />
                </SelectTrigger>
                                  <SelectContent>
                    {filteredZones.map((zone) => (
                      <SelectItem key={zone.id} value={zone.id}>
                        {zone.name} (Floor {zone.floor_number})
                      </SelectItem>
                    ))}
                  </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateDesk} disabled={updateDesk.isPending}>
              {updateDesk.isPending ? 'Updating...' : 'Update Desk'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Desk</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{selectedDesk?.name}"? This action cannot be undone.
              {selectedDesk?.status === 'occupied' && (
                <div className="mt-2 p-2 bg-orange-50 border border-orange-200 rounded text-orange-800 text-sm">
                  ⚠️ Warning: This desk is currently occupied.
                </div>
              )}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleConfirmDelete} 
              disabled={deleteDesk.isPending}
            >
              {deleteDesk.isPending ? 'Deleting...' : 'Delete Desk'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* QR Code Generator Dialog */}
      <Dialog open={isQRCodeDialogOpen} onOpenChange={setIsQRCodeDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Generate QR Code</DialogTitle>
            <DialogDescription>
              Generate a QR code for desk "{selectedDesk?.name}" that users can scan to check in
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {selectedDesk && (
              <DeskQRCodeGenerator
                desk={{
                  id: selectedDesk.id,
                  name: selectedDesk.name
                }}
              />
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsQRCodeDialogOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Inventory Management Dialog */}
      <Dialog open={isInventoryDialogOpen} onOpenChange={setIsInventoryDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Manage Inventory - {selectedDesk?.name}</DialogTitle>
            <DialogDescription>
              Manage items and equipment assigned to this desk
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {selectedDesk && (
              <DeskInventoryManager desk={selectedDesk} />
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsInventoryDialogOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Stats Cards - Updated to show filtered results */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {selectedFloor === 'all' ? 'Total Desks' : `Floor ${selectedFloor} Desks`}
            </CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredDesks.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Available</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {filteredDesks.filter(d => d.status === 'available').length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Occupied</CardTitle>
            <XCircle className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {filteredDesks.filter(d => d.status === 'occupied').length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Maintenance</CardTitle>
            <Wrench className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {filteredDesks.filter(d => d.status === 'maintenance').length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Desks Table */}
      <Card>
        <CardHeader>
          <CardTitle>
            {selectedFloor === 'all' ? 'All Desks' : `Floor ${selectedFloor} Desks`}
          </CardTitle>
          <CardDescription>
            Manage desk configurations and availability
            {selectedFloor !== 'all' && ` for Floor ${selectedFloor}`}
          </CardDescription>
          <div className="flex items-center space-x-2">
            <Search className="h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={`Search ${selectedFloor === 'all' ? 'all' : `floor ${selectedFloor}`} desks...`}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-sm"
            />
          </div>
        </CardHeader>
        <CardContent>
          {Object.keys(filteredGroupedData).length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              {searchTerm
                ? `No desks found matching "${searchTerm}" ${selectedFloor !== 'all' ? `on Floor ${selectedFloor}` : ''}.`
                : selectedFloor !== 'all'
                  ? `No desks found on Floor ${selectedFloor}.`
                  : 'No desks found.'
              }
            </div>
          ) : (
            <div className="space-y-6">
              {Object.entries(filteredGroupedData)
                .sort(([a], [b]) => parseInt(a) - parseInt(b))
                .map(([floorNum, floorData]) => (
                <div key={floorNum} className="space-y-4">
                  <div className="flex items-center gap-2 pb-2 border-b">
                    <Building2 className="h-5 w-5 text-muted-foreground" />
                    <h3 className="text-lg font-semibold">Floor {floorNum}</h3>
                    <Badge variant="secondary" className="ml-2">
                      {Object.values(floorData).reduce((total, zone) => total + zone.desks.length, 0)} desks
                    </Badge>
                  </div>

                  {Object.entries(floorData)
                    .sort(([, a], [, b]) => a.zone.name.localeCompare(b.zone.name))
                    .map(([zoneId, zoneData]) => (
                    <div key={zoneId} className="space-y-3">
                      <div className="flex items-center gap-2 pl-4">
                        <div className="w-3 h-3 bg-primary/20 rounded-full"></div>
                        <h4 className="font-medium text-primary">{zoneData.zone.name}</h4>
                        <Badge variant="outline" className="ml-2">
                          {zoneData.desks.length} desks
                        </Badge>
                      </div>

                      <div className="pl-8">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Desk Name</TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead>Status Actions</TableHead>
                              <TableHead>Manage</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {zoneData.desks.map((desk) => (
                              <TableRow key={desk.id}>
                                <TableCell className="font-medium">
                                  <div className="flex items-center gap-2">
                                    {getStatusIcon(desk.status)}
                                    {desk.name}
                                  </div>
                                </TableCell>
                                <TableCell>
                                  {getStatusBadge(desk.status)}
                                </TableCell>
                                <TableCell>
                                  <Select
                                    value={desk.status}
                                    onValueChange={(value: 'available' | 'occupied' | 'maintenance') =>
                                      handleStatusChange(desk.id, value)
                                    }
                                  >
                                    <SelectTrigger className="w-32">
                                      <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="available">Available</SelectItem>
                                      <SelectItem value="occupied">Occupied</SelectItem>
                                      <SelectItem value="maintenance">Maintenance</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </TableCell>
                                <TableCell>
                                  <div className="flex gap-2 flex-wrap">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handleInventoryClick(desk)}
                                      title="Manage Inventory"
                                    >
                                      <Package className="h-4 w-4" />
                                    </Button>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handleQRCodeClick(desk)}
                                      title="Generate QR Code"
                                    >
                                      <QrCode className="h-4 w-4" />
                                    </Button>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handleEditDesk(desk)}
                                      title="Edit Desk"
                                    >
                                      <Edit className="h-4 w-4" />
                                    </Button>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handleDeleteClick(desk)}
                                      className="text-red-600 hover:text-red-700"
                                      title="Delete Desk"
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    </div>
                  ))}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Item Types Management */}
      <ItemTypesManager />
    </div>
  );
} 