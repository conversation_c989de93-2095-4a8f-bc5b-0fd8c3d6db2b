import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { ItemType } from '@/types';
import { Plus, Edit, Trash2, Setting<PERSON>, CheckCircle, XCircle } from 'lucide-react';

export function ItemTypesManager() {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedItemType, setSelectedItemType] = useState<ItemType | null>(null);
  const [newItemType, setNewItemType] = useState({
    name: '',
    description: '',
    is_default: false
  });
  const [editItemType, setEditItemType] = useState({
    name: '',
    description: '',
    is_default: false
  });

  const queryClient = useQueryClient();

  // Fetch item types
  const { data: itemTypes, isLoading } = useQuery({
    queryKey: ['item-types'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('item_types')
        .select('*')
        .order('name', { ascending: true });
      
      if (error) throw error;
      return data as ItemType[];
    },
  });

  // Mutation to add new item type
  const addItemType = useMutation({
    mutationFn: async (itemType: typeof newItemType) => {
      const { error } = await supabase
        .from('item_types')
        .insert({
          name: itemType.name,
          description: itemType.description || null,
          is_default: itemType.is_default
        });
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['item-types'] });
      toast.success('Item type added successfully');
      setIsAddDialogOpen(false);
      setNewItemType({ name: '', description: '', is_default: false });
    },
    onError: (error: any) => {
      toast.error('Failed to add item type: ' + error.message);
    },
  });

  // Mutation to update item type
  const updateItemType = useMutation({
    mutationFn: async ({ itemTypeId, updates }: { itemTypeId: string; updates: typeof editItemType }) => {
      const { error } = await supabase
        .from('item_types')
        .update({
          name: updates.name,
          description: updates.description || null,
          is_default: updates.is_default
        })
        .eq('id', itemTypeId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['item-types'] });
      toast.success('Item type updated successfully');
      setIsEditDialogOpen(false);
      setSelectedItemType(null);
    },
    onError: (error: any) => {
      toast.error('Failed to update item type: ' + error.message);
    },
  });

  // Mutation to delete item type
  const deleteItemType = useMutation({
    mutationFn: async (itemTypeId: string) => {
      const { error } = await supabase
        .from('item_types')
        .delete()
        .eq('id', itemTypeId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['item-types'] });
      toast.success('Item type deleted successfully');
    },
    onError: (error: any) => {
      toast.error('Failed to delete item type: ' + error.message);
    },
  });

  const handleAddItemType = () => {
    if (!newItemType.name.trim()) {
      toast.error('Please enter an item type name');
      return;
    }
    addItemType.mutate(newItemType);
  };

  const handleEditItemType = (itemType: ItemType) => {
    setSelectedItemType(itemType);
    setEditItemType({
      name: itemType.name,
      description: itemType.description || '',
      is_default: itemType.is_default
    });
    setIsEditDialogOpen(true);
  };

  const handleUpdateItemType = () => {
    if (!editItemType.name.trim() || !selectedItemType) {
      toast.error('Please enter an item type name');
      return;
    }
    updateItemType.mutate({
      itemTypeId: selectedItemType.id,
      updates: editItemType
    });
  };

  const handleDeleteItemType = (itemType: ItemType) => {
    if (confirm(`Are you sure you want to delete "${itemType.name}"? This will also remove all associated desk items.`)) {
      deleteItemType.mutate(itemType.id);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Item Types
            </CardTitle>
            <CardDescription>
              Manage categories of equipment and items that can be assigned to desks
            </CardDescription>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Item Type
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New Item Type</DialogTitle>
                <DialogDescription>
                  Create a new category of equipment or items
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">
                    Name *
                  </Label>
                  <Input
                    id="name"
                    value={newItemType.name}
                    onChange={(e) => setNewItemType({ ...newItemType, name: e.target.value })}
                    className="col-span-3"
                    placeholder="e.g., Monitor, Keyboard, Mouse"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="description" className="text-right">
                    Description
                  </Label>
                  <Textarea
                    id="description"
                    value={newItemType.description}
                    onChange={(e) => setNewItemType({ ...newItemType, description: e.target.value })}
                    className="col-span-3"
                    placeholder="Optional description"
                    rows={3}
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="is-default" className="text-right">
                    Default Item
                  </Label>
                  <div className="col-span-3 flex items-center space-x-2">
                    <Switch
                      id="is-default"
                      checked={newItemType.is_default}
                      onCheckedChange={(checked) => setNewItemType({ ...newItemType, is_default: checked })}
                    />
                    <Label htmlFor="is-default" className="text-sm text-muted-foreground">
                      Automatically add to new desks
                    </Label>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button onClick={handleAddItemType} disabled={addItemType.isPending}>
                  {addItemType.isPending ? 'Adding...' : 'Add Item Type'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {!itemTypes || itemTypes.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Settings className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No item types configured yet.</p>
            <p className="text-sm">Click "Add Item Type" to start defining equipment categories.</p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Default Item</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {itemTypes.map((itemType) => (
                <TableRow key={itemType.id}>
                  <TableCell className="font-medium">
                    {itemType.name}
                  </TableCell>
                  <TableCell>
                    {itemType.description ? (
                      <span className="text-sm">{itemType.description}</span>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {itemType.is_default ? (
                        <>
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <Badge className="bg-green-500">Yes</Badge>
                        </>
                      ) : (
                        <>
                          <XCircle className="h-4 w-4 text-gray-400" />
                          <Badge variant="secondary">No</Badge>
                        </>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditItemType(itemType)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteItemType(itemType)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}

        {/* Edit Item Type Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Item Type</DialogTitle>
              <DialogDescription>
                Update item type details
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-name" className="text-right">
                  Name *
                </Label>
                <Input
                  id="edit-name"
                  value={editItemType.name}
                  onChange={(e) => setEditItemType({ ...editItemType, name: e.target.value })}
                  className="col-span-3"
                  placeholder="e.g., Monitor, Keyboard, Mouse"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-description" className="text-right">
                  Description
                </Label>
                <Textarea
                  id="edit-description"
                  value={editItemType.description}
                  onChange={(e) => setEditItemType({ ...editItemType, description: e.target.value })}
                  className="col-span-3"
                  placeholder="Optional description"
                  rows={3}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-is-default" className="text-right">
                  Default Item
                </Label>
                <div className="col-span-3 flex items-center space-x-2">
                  <Switch
                    id="edit-is-default"
                    checked={editItemType.is_default}
                    onCheckedChange={(checked) => setEditItemType({ ...editItemType, is_default: checked })}
                  />
                  <Label htmlFor="edit-is-default" className="text-sm text-muted-foreground">
                    Automatically add to new desks
                  </Label>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleUpdateItemType} disabled={updateItemType.isPending}>
                {updateItemType.isPending ? 'Updating...' : 'Update Item Type'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
} 