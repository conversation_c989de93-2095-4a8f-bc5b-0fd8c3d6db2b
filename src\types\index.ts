import { Database } from './database';

export type User = Database['public']['Tables']['users']['Row'];
export type Zone = Database['public']['Tables']['zones']['Row'];
export type Desk = Database['public']['Tables']['desks']['Row'];
export type Booking = Database['public']['Tables']['bookings']['Row'];
export type Settings = Database['public']['Tables']['settings']['Row'];
export type ItemType = Database['public']['Tables']['item_types']['Row'];
export type DeskItem = Database['public']['Tables']['desk_items']['Row'];

export interface DeskWithZone extends Desk {
  zone: Zone;
}

export interface DeskItemWithType extends DeskItem {
  item_type: ItemType;
}

export interface DeskWithInventory extends DeskWithZone {
  desk_items?: DeskItemWithType[];
}

export interface BookingWithDetails extends Booking {
  desk: DeskWithZone;
  user: User;
}

export interface BookingWithUser extends Booking {
  users?: User;
}

export interface FloorPlan {
  id: string;
  name: string;
  floor_number: number;
  desks: DeskWithZone[];
}

export type DeskStatus = 'available' | 'occupied' | 'maintenance';
export type BookingStatus = 'booked' | 'checked-in' | 'cancelled';
export type UserRole = 'employee' | 'admin';
export type ItemCondition = 'good' | 'fair' | 'poor' | 'maintenance';