import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, LineChart, Line, PieChart, Pie, Cell } from 'recharts';
import { Download, BarChart3, TrendingUp, Users, Building2, Calendar, Hash, TableIcon, Mail, User } from 'lucide-react';
import { toast } from 'sonner';

interface QueryResult {
  query?: string;
  sql?: string;
  results?: any[];
  data?: any[];
  type?: 'table' | 'chart' | 'metric' | 'both' | 'users';
  visualization?: 'table' | 'chart' | 'metric' | 'both' | 'users';
  title?: string;
  chartType?: 'bar' | 'line' | 'pie' | 'area';
  columns?: string[];
  summary?: string;
  explanation?: string;
  loading?: boolean;
  error?: string;
}

interface QueryResultDisplayProps {
  result: QueryResult;
  onExport?: () => void;
}

const CHART_COLORS = [
  '#8B5CF6', '#06B6D4', '#10B981', '#F59E0B', '#EF4444', '#8B5A2B', '#EC4899', '#6366F1'
];

const chartConfig = {
  value: {
    label: 'Value',
    color: 'hsl(var(--chart-1))',
  },
  count: {
    label: 'Count',
    color: 'hsl(var(--chart-2))',
  },
};

export function QueryResultDisplay({ result, onExport }: QueryResultDisplayProps) {
  if (result.error) {
    return (
      <Card className="border-destructive/20 bg-destructive/5">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-destructive">
            <Hash className="h-5 w-5" />
            Query Error
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-destructive">{result.error}</p>
        </CardContent>
      </Card>
    );
  }

  // Handle both 'results' and 'data' properties for backward compatibility
  const queryData = result.results || result.data || [];
  
  if (!queryData || queryData.length === 0) {
    return (
      <Card className="border-muted">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            No Results
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            Your query executed successfully but returned no results. Try adjusting your question or date range.
          </p>
        </CardContent>
      </Card>
    );
  }

  // Detect if the data contains user information
  const isUserData = () => {
    if (!queryData || queryData.length === 0) return false;
    
    const firstRow = queryData[0];
    const keys = Object.keys(firstRow);
    
    // Check for common user fields
    const userFields = ['email', 'full_name', 'role', 'department', 'avatar_url', 'user_id', 'first_name', 'last_name'];
    const hasUserFields = userFields.some(field => keys.includes(field));
    
    // Also check if we have email-like patterns
    const hasEmailPattern = keys.some(key => 
      key.toLowerCase().includes('email') || 
      (typeof firstRow[key] === 'string' && firstRow[key].includes('@'))
    );
    
    return hasUserFields || hasEmailPattern;
  };

  const exportData = () => {
    if (onExport) {
      onExport();
    } else {
      // Default export functionality
      const csvContent = convertToCSV(queryData);
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `query-results-${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      toast.success('Results exported successfully!');
    }
  };

  const convertToCSV = (data: any[]): string => {
    if (!data || data.length === 0) return '';
    
    const headers = Object.keys(data[0]);
    const csvRows = [
      headers.join(','),
      ...data.map(row => 
        headers.map(header => {
          const value = row[header];
          return typeof value === 'string' ? `"${value}"` : value;
        }).join(',')
      )
    ];
    
    return csvRows.join('\n');
  };

  const renderVisualization = () => {
    // Auto-detect user data and override visualization type
    if (isUserData()) {
      return renderUserCardsView();
    }
    
    const visualizationType = result.visualization || result.type || 'table';
    switch (visualizationType) {
      case 'users':
        return renderUserCardsView();
      case 'metric':
        return renderMetricView();
      case 'chart':
        return renderChartView();
      case 'both':
        return renderBothView();
      case 'table':
      default:
        return renderTableView();
    }
  };

  const renderBothView = () => {
    return (
      <div className="space-y-6">
        {/* Chart View */}
        <div>
          <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Chart View
          </h3>
          {renderChartView()}
        </div>

        {/* Table View */}
        <div>
          <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
            <TableIcon className="h-5 w-5" />
            Data Table
          </h3>
          {renderTableView()}
        </div>
      </div>
    );
  };

  const renderUserCardsView = () => {
    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2 mb-4">
          <Users className="h-5 w-5 text-muted-foreground" />
          <span className="text-sm font-medium text-muted-foreground">
            {queryData.length} {queryData.length === 1 ? 'user' : 'users'} found
          </span>
        </div>
        
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {queryData.map((user, index) => {
            // Extract user information with fallbacks
            const email = user.email || user.user_email || user.user_id || '';
            const fullName = user.full_name || user.name || user.first_name || user.last_name || 
                           `${user.first_name || ''} ${user.last_name || ''}`.trim() || 
                           email.split('@')[0] || `User ${index + 1}`;
            const role = user.role || user.user_role || 'Employee';
            const department = user.department || user.dept || user.team || 'Unknown';
            const avatarUrl = user.avatar_url || user.avatar || '';
            
            // Get initials for fallback avatar
            const getInitials = (name: string) => {
              return name
                .split(' ')
                .map(n => n[0])
                .join('')
                .toUpperCase()
                .slice(0, 2);
            };
            
            // Additional user stats/info
            const additionalInfo = Object.entries(user)
              .filter(([key, value]) => 
                !['email', 'full_name', 'name', 'first_name', 'last_name', 'role', 'department', 'avatar_url', 'avatar', 'user_email', 'user_role', 'dept', 'team'].includes(key) &&
                value !== null && value !== undefined && value !== ''
              )
              .slice(0, 3); // Limit to 3 additional fields
            
            return (
              <Card key={index} className="hover:shadow-md transition-shadow">
                <CardContent className="pt-6">
                  <div className="flex items-start space-x-4">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={avatarUrl} alt={fullName} />
                      <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                        {getInitials(fullName)}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div className="flex-1 min-w-0">
                      <div className="space-y-2">
                        <div>
                          <h3 className="font-semibold text-base truncate" title={fullName}>
                            {fullName}
                          </h3>
                          {email && (
                            <div className="flex items-center text-sm text-muted-foreground mt-1">
                              <Mail className="h-3 w-3 mr-1 flex-shrink-0" />
                              <span className="truncate" title={email}>{email}</span>
                            </div>
                          )}
                        </div>
                        
                        <div className="flex flex-wrap gap-1">
                          <Badge variant="secondary" className="text-xs">
                            {role}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            <Building2 className="h-3 w-3 mr-1" />
                            {department}
                          </Badge>
                        </div>
                        
                        {additionalInfo.length > 0 && (
                          <div className="space-y-1 pt-2 border-t">
                            {additionalInfo.map(([key, value]) => (
                              <div key={key} className="flex justify-between text-xs">
                                <span className="text-muted-foreground capitalize">
                                  {key.replace(/_/g, ' ')}:
                                </span>
                                <span className="font-medium">
                                  {formatCellValue(value)}
                                </span>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
        
        {queryData.length > 12 && (
          <div className="text-center pt-4">
            <p className="text-sm text-muted-foreground">
              Showing {Math.min(queryData.length, 12)} of {queryData.length} users
            </p>
          </div>
        )}
      </div>
    );
  };

  const renderMetricView = () => {
    // For single metric results
    if (queryData.length === 1 && Object.keys(queryData[0]).length <= 2) {
      const data = queryData[0];
      const keys = Object.keys(data);
      const valueKey = keys.find(k => typeof data[k] === 'number') || keys[0];
      const labelKey = keys.find(k => k !== valueKey) || keys[0];
      
      return (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {labelKey ? data[labelKey] : 'Result'}
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data[valueKey]}</div>
              <p className="text-xs text-muted-foreground">
                Based on your query
              </p>
            </CardContent>
          </Card>
        </div>
      );
    }
    
    // For multiple metrics
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {queryData.slice(0, 6).map((item, index) => {
          const keys = Object.keys(item);
          const valueKey = keys.find(k => typeof item[k] === 'number') || keys[0];
          const labelKey = keys.find(k => k !== valueKey) || keys[0];
          
          return (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {labelKey ? item[labelKey] : `Item ${index + 1}`}
                </CardTitle>
                <Hash className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{item[valueKey]}</div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    );
  };

  const renderChartView = () => {
    if (queryData.length === 0) return null;
    
    const data = queryData;
    const keys = Object.keys(data[0]);
    const xKey = keys[0];
    const yKey = keys.find(k => typeof data[0][k] === 'number') || keys[1];
    
    // Determine chart type based on data characteristics
    const isTimeSeriesData = keys.some(k => k.includes('date') || k.includes('time'));
    const hasMultipleNumericFields = keys.filter(k => typeof data[0][k] === 'number').length > 1;
    
    if (isTimeSeriesData) {
      return (
        <ChartContainer config={chartConfig} className="h-80">
          <LineChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey={xKey} />
            <YAxis />
            <ChartTooltip content={<ChartTooltipContent />} />
            <Line
              type="monotone"
              dataKey={yKey}
              stroke={CHART_COLORS[0]}
              strokeWidth={2}
              dot={{ r: 4 }}
            />
          </LineChart>
        </ChartContainer>
      );
    }
    
    if (data.length <= 10) {
      return (
        <ChartContainer config={chartConfig} className="h-80">
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey={xKey} />
            <YAxis />
            <ChartTooltip content={<ChartTooltipContent />} />
            <Bar dataKey={yKey} radius={[4, 4, 0, 0]}>
              {data.map((_, index) => (
                <Cell key={`cell-${index}`} fill={CHART_COLORS[index % CHART_COLORS.length]} />
              ))}
            </Bar>
          </BarChart>
        </ChartContainer>
      );
    }
    
    return renderTableView();
  };

  const renderTableView = () => {
    const data = queryData;
    const headers = Object.keys(data[0]);
    
    return (
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              {headers.map((header) => (
                <TableHead key={header} className="capitalize">
                  {header.replace(/_/g, ' ')}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map((row, index) => (
              <TableRow key={index}>
                {headers.map((header) => (
                  <TableCell key={header}>
                    {formatCellValue(row[header])}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  };

  const formatCellValue = (value: any): React.ReactNode => {
    if (value === null || value === undefined) {
      return <span className="text-muted-foreground">-</span>;
    }
    
    if (typeof value === 'boolean') {
      return <Badge variant={value ? 'default' : 'secondary'}>{value ? 'Yes' : 'No'}</Badge>;
    }
    
    if (typeof value === 'number') {
      return value.toLocaleString();
    }
    
    if (typeof value === 'string' && value.includes('@')) {
      return <span className="font-mono text-sm">{value}</span>;
    }
    
    return value;
  };

  const isDisplayingUsers = isUserData();

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              {isDisplayingUsers ? (
                <Users className="h-5 w-5" />
              ) : (
                <BarChart3 className="h-5 w-5" />
              )}
              {isDisplayingUsers ? 'User Results' : 'Query Results'}
              <Badge variant="outline" className="ml-2">
                {queryData.length} {queryData.length === 1 ? (isDisplayingUsers ? 'user' : 'result') : (isDisplayingUsers ? 'users' : 'results')}
              </Badge>
            </CardTitle>
            <CardDescription className="mt-2">
              {result.query || result.title || (isDisplayingUsers ? 'User Information' : 'Query Results')}
            </CardDescription>
          </div>
          <Button variant="outline" size="sm" onClick={exportData}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {renderVisualization()}
      </CardContent>
    </Card>
  );
} 