import React, { useState, useRef, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { Save, Move, RotateCcw } from 'lucide-react';
import { Zone, DeskWithZone } from '@/types';

interface FloorPlanLayoutEditorProps {
  zone: Zone;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

interface DeskPosition {
  id: string;
  name: string;
  x: number;
  y: number;
  isDragging: boolean;
}

export function FloorPlanLayoutEditor({ zone, isOpen, onOpenChange }: FloorPlanLayoutEditorProps) {
  const [desks, setDesks] = useState<DeskPosition[]>([]);
  const [draggedDesk, setDraggedDesk] = useState<string | null>(null);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageDimensions, setImageDimensions] = useState({ width: 0, height: 0 });
  const imageRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const queryClient = useQueryClient();

  // Fetch desks for this zone
  const { data: zoneDesks, isLoading } = useQuery({
    queryKey: ['zone-desks', zone.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('desks')
        .select('*')
        .eq('zone_id', zone.id)
        .order('name');
      
      if (error) throw error;
      return data as DeskWithZone[];
    },
    enabled: isOpen
  });

  // Initialize desk positions when data loads
  useEffect(() => {
    if (zoneDesks && imageLoaded) {
      setDesks(zoneDesks.map((desk, index) => ({
        id: desk.id,
        name: desk.name,
        x: desk.coordinates?.x || (100 + (index % 5) * 80),
        y: desk.coordinates?.y || (100 + Math.floor(index / 5) * 80),
        isDragging: false
      })));
    }
  }, [zoneDesks, imageLoaded]);

  // Mutation to save desk positions
  const saveDeskPositions = useMutation({
    mutationFn: async (positions: DeskPosition[]) => {
      const updates = positions.map(desk => ({
        id: desk.id,
        coordinates: { x: desk.x, y: desk.y }
      }));

      for (const update of updates) {
        const { error } = await supabase
          .from('desks')
          .update({ coordinates: update.coordinates })
          .eq('id', update.id);
        
        if (error) throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['desks-with-zones'] });
      queryClient.invalidateQueries({ queryKey: ['zone-desks'] });
      toast.success('Desk positions saved successfully!');
    },
    onError: (error) => {
      toast.error('Failed to save positions: ' + error.message);
    }
  });

  const handleImageLoad = () => {
    if (imageRef.current) {
      setImageDimensions({
        width: imageRef.current.naturalWidth,
        height: imageRef.current.naturalHeight
      });
      setImageLoaded(true);
    }
  };

  const handleMouseDown = (deskId: string, event: React.MouseEvent) => {
    event.preventDefault();
    setDraggedDesk(deskId);
    setDesks(prev => prev.map(desk => 
      desk.id === deskId ? { ...desk, isDragging: true } : desk
    ));
  };

  const handleMouseMove = (event: React.MouseEvent) => {
    if (!draggedDesk || !containerRef.current || !imageRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const imageRect = imageRef.current.getBoundingClientRect();
    
    // Calculate position relative to the image
    const x = ((event.clientX - imageRect.left) / imageRect.width) * imageDimensions.width;
    const y = ((event.clientY - imageRect.top) / imageRect.height) * imageDimensions.height;

    // Keep within bounds
    const clampedX = Math.max(20, Math.min(imageDimensions.width - 20, x));
    const clampedY = Math.max(20, Math.min(imageDimensions.height - 20, y));

    setDesks(prev => prev.map(desk => 
      desk.id === draggedDesk 
        ? { ...desk, x: clampedX, y: clampedY }
        : desk
    ));
  };

  const handleMouseUp = () => {
    setDraggedDesk(null);
    setDesks(prev => prev.map(desk => ({ ...desk, isDragging: false })));
  };

  const handleSave = () => {
    saveDeskPositions.mutate(desks);
  };

  const handleReset = () => {
    if (zoneDesks) {
      setDesks(zoneDesks.map((desk, index) => ({
        id: desk.id,
        name: desk.name,
        x: 100 + (index % 5) * 80,
        y: 100 + Math.floor(index / 5) * 80,
        isDragging: false
      })));
    }
  };

  if (!zone.photo) {
    return (
      <Dialog open={isOpen} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Configure Floor Plan Layout</DialogTitle>
          </DialogHeader>
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
              <Move className="w-8 h-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-semibold mb-2">No Floor Plan Image</h3>
            <p className="text-muted-foreground mb-4">
              Please upload a floor plan image for this zone first.
            </p>
            <Button onClick={() => onOpenChange(false)}>
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Move className="w-5 h-5" />
            Configure Layout: {zone.name}
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Instructions */}
          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <p className="text-sm text-blue-700 dark:text-blue-300">
              <strong>Instructions:</strong> Drag the desk circles to position them on your floor plan. 
              The positions will be saved and used in the advanced booking view.
            </p>
          </div>

          {/* Controls */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Badge variant="secondary">
                {desks.length} desk{desks.length !== 1 ? 's' : ''}
              </Badge>
              <Badge variant="outline">
                Floor {zone.floor_number}
              </Badge>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleReset}
                disabled={isLoading}
              >
                <RotateCcw className="w-4 h-4 mr-2" />
                Reset Positions
              </Button>
              <Button
                onClick={handleSave}
                disabled={saveDeskPositions.isPending}
                size="sm"
              >
                <Save className="w-4 h-4 mr-2" />
                {saveDeskPositions.isPending ? 'Saving...' : 'Save Layout'}
              </Button>
            </div>
          </div>

          {/* Floor Plan Editor */}
          <div 
            ref={containerRef}
            className="relative border rounded-lg overflow-hidden bg-white"
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
            style={{ cursor: draggedDesk ? 'grabbing' : 'default' }}
          >
            <img
              ref={imageRef}
              src={zone.photo}
              alt={`${zone.name} floor plan`}
              className="w-full h-auto max-h-[60vh] object-contain"
              onLoad={handleImageLoad}
              draggable={false}
            />
            
            {/* Desk Circles Overlay */}
            {imageLoaded && desks.map((desk) => {
              const imageRect = imageRef.current?.getBoundingClientRect();
              if (!imageRect) return null;

              const xPercent = (desk.x / imageDimensions.width) * 100;
              const yPercent = (desk.y / imageDimensions.height) * 100;

              return (
                <div
                  key={desk.id}
                  className={`absolute transform -translate-x-1/2 -translate-y-1/2 ${
                    desk.isDragging ? 'z-50' : 'z-10'
                  }`}
                  style={{
                    left: `${xPercent}%`,
                    top: `${yPercent}%`,
                    cursor: draggedDesk === desk.id ? 'grabbing' : 'grab'
                  }}
                >
                  <div
                    className={`w-12 h-12 rounded-full bg-blue-500 hover:bg-blue-600 
                      flex items-center justify-center text-white text-xs font-bold
                      shadow-lg hover:shadow-xl transition-all duration-200
                      ring-2 ring-white hover:ring-blue-200
                      ${desk.isDragging ? 'scale-110 shadow-2xl' : 'hover:scale-105'}
                    `}
                    onMouseDown={(e) => handleMouseDown(desk.id, e)}
                  >
                    {desk.name.replace('Desk ', '').replace(/[^0-9A-Z]/gi, '')}
                  </div>
                  
                  {/* Desk label */}
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1">
                    <div className="bg-black/75 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
                      {desk.name}
                    </div>
                  </div>
                </div>
              );
            })}
            
            {/* Loading overlay */}
            {!imageLoaded && (
              <div className="absolute inset-0 bg-muted animate-pulse rounded-lg" />
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 